<template>
  <div style="width: 100vw;height: 100vh;">
    <MapGaoDe ref="mapRef" style="height: 100%;"/>
  </div>
</template>

<script lang="ts" setup>
import {cities} from '@/cities';
import MapGaoDe from '@/MapGaoDe.vue';
import {onMounted, ref} from 'vue';
import '@amap/amap-jsapi-types';

let mapRef = ref();
let map, mass;

let style = [
  {zIndex: 0, url: './public/poi/01.png', rotation: 0, size: new AMap.Size(24, 27), anchor: new AMap.Pixel(12, 20)},
  {zIndex: 1, url: './public/poi/02.png', rotation: 0, size: new AMap.Size(24, 27), anchor: new AMap.Pixel(12, 20)},
  {zIndex: 2, url: './public/poi/03.png', rotation: 0, size: new AMap.Size(24, 27), anchor: new AMap.Pixel(12, 20)},
  {zIndex: 3, url: './public/poi/04.png', rotation: 0, size: new AMap.Size(24, 27), anchor: new AMap.Pixel(12, 20)},
  {zIndex: 4, url: './public/poi/05.png', rotation: 0, size: new AMap.Size(24, 27), anchor: new AMap.Pixel(12, 20)},
];

onMounted(() => {
  mapRef.value.init().then(() => {
    mass = new AMap.MassMarks([], {cursor: 'pointer', style: style});
    mass.setData(cities);
    mass.setMap(mapRef.value.map);
  });
});
</script>

<script lang="ts">
export default {name: 'Demo9'};
</script>

<style lang="less" scoped></style>
